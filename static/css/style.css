:root {
    --background-color: #f0f2f5;
    --container-bg: #ffffff;
    --header-bg: #f5f5f5;
    --bot-msg-bg: #e9e9eb;
    --user-msg-bg: #0084ff;
    --user-msg-text: #ffffff;
    --text-color: #0d0d0d;
    --border-color: #e0e0e0;
    --font-family: 'Roboto', sans-serif;
}

body {
    font-family: var(--font-family);
    background-color: var(--background-color);
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.chat-container {
    width: 90%;
    max-width: 700px;
    height: 90vh;
    max-height: 800px;
    background-color: var(--container-bg);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-header {
    background-color: var(--header-bg);
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.menu-container {
    position: relative;
}

.menu-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.dropdown-menu {
    display: none;
    position: absolute;
    right: 0;
    top: 40px;
    background-color: var(--container-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 100;
    width: 150px;
    overflow: hidden;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-menu a {
    display: block;
    padding: 12px 16px;
    color: var(--text-color);
    text-decoration: none;
    font-size: 0.9rem;
}

.dropdown-menu a:hover {
    background-color: var(--background-color);
}


.chat-header h2 {
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-color);
}

.chat-box {
    flex-grow: 1;
    padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.message {
    padding: 0.6rem 1rem;
    border-radius: 18px;
    max-width: 80%;
    line-height: 1.4;
    word-wrap: break-word;
}

.message.bot {
    background-color: var(--bot-msg-bg);
    color: var(--text-color);
    align-self: flex-start;
    border-bottom-left-radius: 4px;
}

.message.user {
    background-color: var(--user-msg-bg);
    color: var(--user-msg-text);
    align-self: flex-end;
    border-bottom-right-radius: 4px;
}

/* Stili per il contenuto HTML generato dal bot */
.message.bot p {
    margin: 0;
}
.message.bot a {
    color: #005cbf;
    text-decoration: none;
    font-weight: 500;
}
.message.bot a:hover {
    text-decoration: underline;
}

.chat-input-area {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
}

.chat-form {
    display: flex;
    gap: 0.5rem;
}

#user-input {
    flex-grow: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 18px;
    font-size: 1rem;
}
#user-input:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
}

#send-btn {
    background-color: var(--user-msg-bg);
    color: white;
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.2s;
}
#send-btn:hover:not(:disabled) {
    background-color: #0073e6;
}
#send-btn:disabled {
    background-color: #a0cffc;
    cursor: not-allowed;
}


/* Modal Styles */
.modal {
    position: fixed;
    z-index: 10;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}
.modal-content {
    background-color: #fff;
    padding: 2rem;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    text-align: center;
}
.modal-content input {
    width: calc(100% - 2rem);
    padding: 0.75rem;
    margin-top: 1rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}
.modal-content button {
    background-color: var(--user-msg-bg);
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    width: 100%;
}
.modal-status {
    margin-top: 1rem;
    font-weight: 500;
}
.modal-status.success { color: #28a745; }
.modal-status.error { color: #dc3545; }

/* Stili per la modale fullscreen dei link */
.link-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.link-modal.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
}

.modal-container {
    position: relative;
    width: 95%;
    height: 95%;
    max-width: 1400px;
    max-height: 900px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transform: scale(1);
    transition: transform 0.3s ease;
}

.link-modal.hidden .modal-container {
    transform: scale(0.9);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background-color: var(--primary-color);
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 60px;
}

.modal-nav-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.modal-nav-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 6px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.modal-nav-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.modal-nav-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.modal-title {
    flex: 1;
    text-align: center;
    font-weight: 500;
    font-size: 16px;
    margin: 0 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.modal-title h1 {
    margin: 0;
    font-size: inherit;
    font-weight: inherit;
}

.modal-close-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 6px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.modal-close-btn:hover {
    background: rgba(255, 0, 0, 0.2);
    border-color: rgba(255, 0, 0, 0.3);
}

.modal-content-area {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-color: #f8f9fa;
}

.modal-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background-color: white;
}

.modal-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    z-index: 10;
}

.loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e3e3e3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loader-text {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.modal-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    text-align: center;
    padding: 40px;
    z-index: 10;
}

.error-icon {
    font-size: 48px;
}

.error-title {
    font-size: 20px;
    font-weight: 600;
    color: #dc3545;
    margin: 0;
}

.error-title h2 {
    margin: 0;
    font-size: inherit;
    font-weight: inherit;
    color: inherit;
}

.error-message {
    font-size: 14px;
    color: #666;
    margin: 0;
    max-width: 400px;
}

.retry-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.retry-btn:hover {
    background-color: #0056b3;
}

/* Responsive design per la modale */
@media (max-width: 768px) {
    .modal-container {
        width: 100%;
        height: 100%;
        border-radius: 0;
    }

    .modal-header {
        padding: 12px 16px;
        min-height: 56px;
    }

    .modal-nav-btn {
        width: 32px;
        height: 32px;
        padding: 6px;
    }

    .modal-close-btn {
        width: 36px;
        height: 36px;
        padding: 6px;
    }

    .modal-title {
        font-size: 14px;
        margin: 0 12px;
    }

    .modal-error {
        padding: 20px;
    }

    .error-title {
        font-size: 18px;
    }

    .loader-spinner {
        width: 32px;
        height: 32px;
    }

    .loader-text {
        font-size: 12px;
    }
}

/* Ottimizzazioni per schermi molto piccoli */
@media (max-width: 480px) {
    .modal-nav-controls {
        gap: 4px;
    }

    .modal-nav-btn {
        width: 28px;
        height: 28px;
        padding: 4px;
    }

    .modal-close-btn {
        width: 32px;
        height: 32px;
        padding: 4px;
    }

    .modal-title {
        font-size: 12px;
        margin: 0 8px;
    }

    .modal-header {
        padding: 8px 12px;
        min-height: 48px;
    }

    .error-message {
        font-size: 12px;
    }

    .retry-btn {
        padding: 8px 16px;
        font-size: 12px;
    }
}