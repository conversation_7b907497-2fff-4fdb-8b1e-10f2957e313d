<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assistente Virtuale</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h2>Assistente Virtuale</h2>
            <div class="menu-container">
                <button id="menu-btn" class="menu-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24" height="24"><path d="M12 8.25a1.5 1.5 0 110 3 1.5 1.5 0 010-3zm0 4.5a1.5 1.5 0 110 3 1.5 1.5 0 010-3zm0 4.5a1.5 1.5 0 110 3 1.5 1.5 0 010-3z" /></svg>
                </button>
                <div id="dropdown-menu" class="dropdown-menu">
                    <a href="#" id="clear-chat-btn">Svuota Chat</a>
                    <a href="#" id="restart-chat-btn">Riavvia Chat</a>
                </div>
            </div>
        </div>
        <div class="chat-box" id="chat-box">
            </div>
        <div class="chat-input-area">
            <form id="chat-form" class="chat-form">
                <input type="text" id="user-input" placeholder="Scrivi la tua domanda..." autocomplete="off" disabled>
                <button type="submit" id="send-btn" disabled>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24" height="24"><path d="M3.478 2.405a.75.75 0 00-.926.94l2.432 7.905H13.5a.75.75 0 010 1.5H4.984l-2.432 7.905a.75.75 0 00.926.94 60.519 60.519 0 0018.445-8.986.75.75 0 000-1.218A60.517 60.517 0 003.478 2.405z" /></svg>
                </button>
            </form>
        </div>
    </div>

    <div class="modal" id="product-modal">
        <div class="modal-content">
            <h2>Codice Prodotto Richiesto</h2>
            <p>Per iniziare, inserisci il codice prodotto di cui vuoi consultare la documentazione.</p>
            <form id="product-form">
                <input type="text" id="product-code-input" placeholder="Es: CODICE_PRODOTTO_A" required>
                <button type="submit">Inizia a Chattare</button>
            </form>
            <div id="modal-status" class="modal-status"></div>
        </div>
    </div>

    <!-- Modale fullscreen per i link -->
    <div id="link-modal" class="link-modal hidden" role="dialog" aria-modal="true" aria-labelledby="modal-title">
        <div class="modal-overlay" aria-hidden="true"></div>
        <div class="modal-container">
            <!-- Header della modale -->
            <div class="modal-header">
                <div class="modal-nav-controls">
                    <button id="modal-back-btn" class="modal-nav-btn" title="Indietro (Alt+←)" aria-label="Vai alla pagina precedente" disabled>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
                            <path d="m15 18-6-6 6-6"/>
                        </svg>
                    </button>
                    <button id="modal-forward-btn" class="modal-nav-btn" title="Avanti (Alt+→)" aria-label="Vai alla pagina successiva" disabled>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
                            <path d="m9 18 6-6-6-6"/>
                        </svg>
                    </button>
                    <button id="modal-refresh-btn" class="modal-nav-btn" title="Ricarica (F5)" aria-label="Ricarica la pagina">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                            <path d="M21 3v5h-5"/>
                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                            <path d="M3 21v-5h5"/>
                        </svg>
                    </button>
                </div>
                <h1 class="modal-title" id="modal-title">Caricamento...</h1>
                <button id="modal-close-btn" class="modal-close-btn" title="Chiudi (Esc)" aria-label="Chiudi la modale">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
                        <path d="m18 6-12 12"/>
                        <path d="m6 6 12 12"/>
                    </svg>
                </button>
            </div>

            <!-- Contenuto della modale -->
            <div class="modal-content-area" role="main">
                <!-- Loader -->
                <div id="modal-loader" class="modal-loader" aria-live="polite" aria-label="Caricamento in corso">
                    <div class="loader-spinner" aria-hidden="true"></div>
                    <div class="loader-text">Caricamento in corso...</div>
                </div>

                <!-- Messaggio di errore -->
                <div id="modal-error" class="modal-error hidden" role="alert" aria-live="assertive">
                    <div class="error-icon" aria-hidden="true">⚠️</div>
                    <h2 class="error-title">Errore di caricamento</h2>
                    <div class="error-message" id="modal-error-message">Impossibile caricare il contenuto.</div>
                    <button id="modal-retry-btn" class="retry-btn" aria-label="Riprova a caricare il contenuto">Riprova</button>
                </div>

                <!-- Iframe per il contenuto -->
                <iframe id="modal-iframe" class="modal-iframe hidden" frameborder="0" title="Contenuto del link"></iframe>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/link-modal.js') }}"></script>
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
</body>
</html>